#!/usr/bin/env python3
"""
測試配置檔案載入功能
"""

import sys
import os
sys.path.append('code')

print("開始測試...")

# 首先測試 yaml 模組
try:
    import yaml
    print("✓ PyYAML 模組導入成功")
except ImportError as e:
    print(f"❌ PyYAML 導入失敗: {e}")
    sys.exit(1)

# 測試配置檔案是否存在
if os.path.exists('settings.yaml'):
    print("✓ settings.yaml 檔案存在")
else:
    print("❌ settings.yaml 檔案不存在")
    sys.exit(1)

try:
    from gait_analyzer import GaitAnalyzer
    print("✓ GaitAnalyzer 模組導入成功")

    # 測試 GaitAnalyzer 配置載入
    analyzer = GaitAnalyzer()
    print("✓ GaitAnalyzer 配置載入成功")
    print(f"  角度閾值: {analyzer.angle_thresholds}")
    print(f"  速度閾值: {analyzer.speed_thresholds}")

    # 測試評估功能
    test_angles = {'left_knee': 90, 'right_knee': 85}
    test_speed = 0.2

    level, score, reason = analyzer.evaluate_gait(test_angles, test_speed)
    print(f"\n測試評估結果:")
    print(f"  等級: {level}")
    print(f"  評分: {score:.1f}")
    print(f"  原因: {reason}")

    print("\n✓ 所有測試通過！")

except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 測試失敗: {e}")
    import traceback
    traceback.print_exc()
