# GUI 使用說明

## 概述
這是一個基於 PySide6 的步態檢測系統 GUI 應用程式，提供即時的步態分析和視覺化功能。系統專注於步態數據分析，不顯示攝像頭畫面，提供更專業的分析介面。

## 主要功能

### 1. 步態分析面板
- **位置**: 主要顯示區域
- **包含組件**:
  - 整體步態狀態顯示
  - 下肢角度品質進度條
  - 行走速度進度條
  - 關節角度數值顯示（僅在檢測到下肢時顯示）
  - 行走速度顯示

### 2. 進度條系統
- **下肢角度品質**:
  - 評估膝關節屈曲和伸展角度
  - 僅在檢測到下肢節點時顯示數值
  - 未檢測到下肢時顯示 "No lower limb detected"
- **行走速度**:
  - 評估移動速度
  - 顯示實際速度值 (m/s) 和評分
- **顏色編碼**:
  - 綠色: 優良 (70-100分)
  - 橙色: 良好 (30-70分)
  - 紅色: 需改善 (0-30分)

### 3. 線性評分系統
- **速度評分**: 使用線性縮放，可在 settings.yaml 中調整參數
- **角度評分**: 基於理想角度範圍和懲罰範圍的線性評分
- **可調整參數**:
  - 速度範圍 (最小值、最大值、閾值)
  - 角度理想範圍和懲罰範圍
  - 權重和基準分數

### 4. 智能檢測
- **下肢檢測**: 自動檢測是否有足夠的下肢關鍵點
- **條件顯示**: 只有在檢測到下肢時才顯示角度相關數據
- **即時反饋**: 提供即時的步態分析結果

## 操作指南

### 啟動應用程式
```bash
cd code
python gui_main.py
```

### 使用步驟
1. **啟動程式**: 執行 `gui_main.py`
2. **自動啟動**: 攝像頭會自動啟動進行檢測
3. **開始檢測**: 在攝像頭前方開始行走
4. **觀察結果**: 介面會顯示即時分析結果

### 介面說明
- **步態狀態**: 顯示整體步態評估 (Excellent/Good/Poor)
- **下肢角度品質進度條**: 顯示膝關節角度評分
- **行走速度進度條**: 顯示移動速度評分
- **關節角度數值**: 僅在檢測到下肢時顯示具體角度
- **狀態列**: 顯示系統狀態和檢測信息

## 系統要求

### 硬體要求
- 攝像頭 (內建或外接)
- CPU: 建議 Intel i5 或同等級以上
- RAM: 建議 4GB 以上
- 顯示器: 建議 1024x768 以上解析度

### 軟體要求
- Python 3.8+
- PySide6
- OpenCV
- MediaPipe
- NumPy
- PyYAML (可選)

## 技術特點

### 1. 即時處理
- 使用多線程處理視頻流
- 背景運行攝像頭檢測
- 低延遲的姿態檢測

### 2. 智能分析
- MediaPipe 姿態檢測
- 下肢關鍵點檢測
- 線性評分系統
- 可配置的評分參數

### 3. 用戶友好
- 專注於數據分析的介面
- 清晰的狀態指示
- 條件性顯示避免混淆

## 故障排除

### 常見問題
1. **攝像頭無法啟動**
   - 檢查攝像頭是否被其他程式佔用
   - 確認攝像頭驅動程式正常

2. **下肢檢測不到**
   - 確保光線充足
   - 保持適當的距離 (1-3公尺)
   - 確保下肢在攝像頭視野內
   - 避免背景干擾

3. **程式運行緩慢**
   - 關閉其他佔用 CPU 的程式
   - 降低攝像頭解析度
   - 檢查系統資源使用情況

### 效能優化
- 調整 `settings.yaml` 中的線性縮放參數
- 使用較低的攝像頭解析度
- 關閉不必要的視覺效果
## 配置說明

### settings.yaml 配置檔案
```yaml
# 步態分析參數
gait_analysis:
  angle_thresholds:
    excellent_ranges: [[60, 100], [160, 180]]
    good_ranges: [[40, 120], [140, 180]]
    poor_range: [120, 160]

  speed_thresholds:
    excellent: 0.8
    good: 0.4
    poor: 0.2

  scoring:
    angle_weight: 0.6
    speed_weight: 0.4

  smoothing:
    speed_window: 5
    score_window: 3

# 視覺化設定
visualization:
  progress_bar:
    linear_scaling:
      # 行走速度進度條縮放
      speed:
        min_value: 0.0      # 0% 進度的最小速度
        max_value: 0.5      # 100% 進度的最大速度
        excellent_threshold: 0.3  # 優良評級閾值
        good_threshold: 0.1       # 良好評級閾值

      # 下肢角度進度條縮放
      angle:
        ideal_ranges:
          - [60, 100]   # 膝關節屈曲範圍
          - [160, 180]  # 膝關節伸展範圍
        penalty_ranges:
          - [130, 160]  # 問題範圍
        ideal_weight: 1.0     # 理想範圍權重
        penalty_weight: -0.5  # 懲罰範圍權重
        baseline_score: 50    # 基準分數

# 系統設定
system:
  pixel_to_meter: null  # 自動計算
```

### 線性縮放參數說明
- **speed.min_value/max_value**: 定義速度進度條的範圍
- **angle.ideal_ranges**: 定義理想的膝關節角度範圍
- **angle.penalty_ranges**: 定義需要懲罰的角度範圍
- **權重參數**: 控制不同範圍對最終評分的影響

## 開發說明

### 檔案結構
```
code/
├── gui_main.py          # 主要 GUI 應用程式 (已更新)
├── gui_widgets.py       # 自定義 GUI 組件
├── pose_detection.py    # 姿態檢測模組
├── angle_calculator.py  # 角度計算模組
├── gait_analyzer.py     # 步態分析模組 (已更新線性評分)
├── videocapture.py      # 視頻捕獲模組
└── styles.qss          # 樣式表檔案
```

### 主要更新
1. **移除攝像頭顯示**: 專注於數據分析
2. **線性評分系統**: 更平滑的進度條變化
3. **下肢檢測**: 智能檢測下肢關鍵點
4. **條件顯示**: 只在檢測到下肢時顯示角度數據
5. **可配置參數**: 在 settings.yaml 中調整線性縮放參數

### 擴展功能
- 可以添加更多的步態參數
- 支援數據記錄和分析
- 添加用戶設定介面
- 支援多人同時檢測

## 版本資訊
- 版本: 2.0.0
- 更新日期: 2024年
- 主要更新: 移除攝像頭顯示、線性評分系統、智能下肢檢測
- 開發環境: Python 3.8+, PySide6
