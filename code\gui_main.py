#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PySide6 GUI Main Application for Gait Detection System
"""

import sys
import cv2
import time
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QLabel, QFrame, QStatusBar, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont

# Import our modules
from pose_detection import PoseDetector
from angle_calculator import AngleCalculator
from gait_analyzer import GaitAnalyzer
import videocapture as vc
from gui_widgets import GradientProgressBar


class VideoThread(QThread):
    """Thread for handling video processing"""
    gait_data_ready = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.cap = None
        self.pose_detector = PoseDetector()
        self.angle_calculator = AngleCalculator()
        self.gait_analyzer = GaitAnalyzer()
        self.prev_time = time.time()
        
    def start_capture(self):
        """Start video capture"""
        self.cap = vc.open_camera()
        self.running = True
        self.start()
        
    def stop_capture(self):
        """Stop video capture"""
        self.running = False
        if self.cap:
            self.cap.release()
        self.quit()
        self.wait()
        
    def run(self):
        """Main video processing loop"""
        while self.running:
            if self.cap is None:
                continue
                
            frame = vc.get_camera_frame(self.cap)
            if frame is None:
                continue
                
            # Mirror the frame horizontally
            frame = cv2.flip(frame, 1)
            
            # Detect pose
            pose_landmarks = self.pose_detector.detect(frame)
            
            # Prepare gait data
            gait_data = {
                'pose_landmarks': pose_landmarks,
                'has_person': pose_landmarks is not None,
                'angles': {},
                'speed': 0.0,
                'level': 'poor',
                'score': 0.0,
                'angle_score': 0.0,
                'speed_score': 0.0,
                'reason': ''
            }
            
            if pose_landmarks is not None:
                # Check if lower limb is detected
                has_lower_limb = self.gait_analyzer.has_lower_limb_landmarks(pose_landmarks)

                # Calculate angles
                angles = self.angle_calculator.get_joint_angles(pose_landmarks)
                gait_data['angles'] = angles
                gait_data['has_lower_limb'] = has_lower_limb

                # Calculate speed
                curr_time = time.time()
                dt = curr_time - self.prev_time
                self.prev_time = curr_time
                speed = self.gait_analyzer.calculate_speed(pose_landmarks, dt)
                gait_data['speed'] = speed

                # Evaluate gait with linear scoring
                level, score, reason = self.gait_analyzer.evaluate_gait(angles, speed, has_lower_limb)

                # Use linear scoring methods
                if has_lower_limb:
                    angle_score = self.gait_analyzer._evaluate_angles_linear(angles)
                else:
                    angle_score = 0
                speed_score = self.gait_analyzer._evaluate_speed_linear(speed)

                gait_data.update({
                    'level': level,
                    'score': score,
                    'angle_score': angle_score,
                    'speed_score': speed_score,
                    'reason': reason
                })
            
            # Emit gait data signal only
            self.gait_data_ready.emit(gait_data)
            
            # Small delay to prevent overwhelming the GUI
            self.msleep(33)  # ~30 FPS


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        self.video_thread = VideoThread()
        self.init_ui()
        self.setup_connections()
        self.apply_styles()
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("Gait Detection System")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Main panel - Control panel only (no video display)
        self.setup_control_panel(main_layout)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Click to start camera")
        


    def setup_control_panel(self, parent_layout):
        """Setup control panel with progress bars and status"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Box)
        control_frame.setLineWidth(2)
        control_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(30)
        control_layout.setContentsMargins(40, 40, 40, 40)

        # Title
        title = QLabel("Gait Analysis")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        control_layout.addWidget(title)

        # Overall status
        self.setup_overall_status(control_layout)

        # Progress bars with labels
        self.setup_progress_bars(control_layout)

        # Joint angles display
        self.setup_angles_display(control_layout)

        # Speed display
        self.setup_speed_display(control_layout)

        # Stretch to push everything to top
        control_layout.addStretch()

        parent_layout.addWidget(control_frame, 1)  # 1/3 of the width

    def setup_overall_status(self, parent_layout):
        """Setup overall gait status display"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QVBoxLayout(status_frame)

        # Status label
        status_title = QLabel("Gait Status")
        status_title.setFont(QFont("Arial", 12, QFont.Bold))
        status_title.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(status_title)

        self.status_label = QLabel("No Person Detected")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.status_label.setStyleSheet("color: #E74C3C; padding: 10px;")
        status_layout.addWidget(self.status_label)

        parent_layout.addWidget(status_frame)

    def setup_progress_bars(self, parent_layout):
        """Setup angle and speed progress bars"""
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.StyledPanel)
        progress_layout = QVBoxLayout(progress_frame)

        # Lower Limb Angle Progress Bar
        angle_label = QLabel("Lower Limb Angle Quality")
        angle_label.setFont(QFont("Arial", 14, QFont.Bold))
        angle_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(angle_label)

        angle_desc = QLabel("(Knee flexion and extension angles)")
        angle_desc.setFont(QFont("Arial", 10))
        angle_desc.setAlignment(Qt.AlignCenter)
        angle_desc.setStyleSheet("color: #7F8C8D; margin-bottom: 10px;")
        progress_layout.addWidget(angle_desc)

        self.angle_progress = GradientProgressBar()
        self.angle_progress.setMinimum(0)
        self.angle_progress.setMaximum(100)
        self.angle_progress.setValue(0)
        progress_layout.addWidget(self.angle_progress)

        self.angle_value_label = QLabel("No lower limb detected")
        self.angle_value_label.setAlignment(Qt.AlignCenter)
        self.angle_value_label.setFont(QFont("Arial", 12))
        progress_layout.addWidget(self.angle_value_label)

        # Spacing
        progress_layout.addSpacing(30)

        # Walking Speed Progress Bar
        speed_label = QLabel("Walking Speed")
        speed_label.setFont(QFont("Arial", 14, QFont.Bold))
        speed_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(speed_label)

        speed_desc = QLabel("(Movement velocity assessment)")
        speed_desc.setFont(QFont("Arial", 10))
        speed_desc.setAlignment(Qt.AlignCenter)
        speed_desc.setStyleSheet("color: #7F8C8D; margin-bottom: 10px;")
        progress_layout.addWidget(speed_desc)

        self.speed_progress = GradientProgressBar()
        self.speed_progress.setMinimum(0)
        self.speed_progress.setMaximum(100)
        self.speed_progress.setValue(0)
        progress_layout.addWidget(self.speed_progress)

        self.speed_value_label = QLabel("0.00 m/s")
        self.speed_value_label.setAlignment(Qt.AlignCenter)
        self.speed_value_label.setFont(QFont("Arial", 12))
        progress_layout.addWidget(self.speed_value_label)

        parent_layout.addWidget(progress_frame)

    def setup_angles_display(self, parent_layout):
        """Setup joint angles display"""
        angles_frame = QFrame()
        angles_frame.setFrameStyle(QFrame.StyledPanel)
        angles_layout = QVBoxLayout(angles_frame)

        angles_title = QLabel("Joint Angles")
        angles_title.setFont(QFont("Arial", 12, QFont.Bold))
        angles_title.setAlignment(Qt.AlignCenter)
        angles_layout.addWidget(angles_title)

        # Create labels for angles
        self.left_knee_label = QLabel("Left Knee: 0.0°")
        self.left_knee_label.setFont(QFont("Arial", 10))
        angles_layout.addWidget(self.left_knee_label)

        self.right_knee_label = QLabel("Right Knee: 0.0°")
        self.right_knee_label.setFont(QFont("Arial", 10))
        angles_layout.addWidget(self.right_knee_label)

        parent_layout.addWidget(angles_frame)

    def setup_speed_display(self, parent_layout):
        """Setup speed display"""
        speed_frame = QFrame()
        speed_frame.setFrameStyle(QFrame.StyledPanel)
        speed_layout = QVBoxLayout(speed_frame)

        speed_title = QLabel("Walking Speed")
        speed_title.setFont(QFont("Arial", 12, QFont.Bold))
        speed_title.setAlignment(Qt.AlignCenter)
        speed_layout.addWidget(speed_title)

        self.speed_display_label = QLabel("0.00 m/s")
        self.speed_display_label.setAlignment(Qt.AlignCenter)
        self.speed_display_label.setFont(QFont("Arial", 14, QFont.Bold))
        speed_layout.addWidget(self.speed_display_label)

        parent_layout.addWidget(speed_frame)

    def setup_connections(self):
        """Setup signal connections"""
        # Only connect gait data, no video display needed
        self.video_thread.gait_data_ready.connect(self.update_gait_data)

        # Auto-start camera
        self.start_camera()

    def start_camera(self):
        """Start camera automatically"""
        if not self.video_thread.running:
            self.video_thread.start_capture()
            self.status_bar.showMessage("Camera started - Gait analysis active")

    def update_gait_data(self, gait_data):
        """Update all gait-related displays"""
        self.current_pose_landmarks = gait_data['pose_landmarks']

        if gait_data['has_person']:
            # Update overall status
            level = gait_data['level']
            reason = gait_data['reason']

            level_colors = {
                'excellent': '#27AE60',  # Green
                'good': '#F39C12',       # Orange
                'poor': '#E74C3C'        # Red
            }

            level_text = {
                'excellent': 'Excellent Gait',
                'good': 'Good Gait',
                'poor': 'Poor Gait'
            }

            status_text = level_text.get(level, 'Unknown')
            if reason and level != 'excellent':
                status_text += f" ({reason})"

            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"color: {level_colors.get(level, '#E74C3C')}; padding: 10px;")

            # Update progress bars
            angle_score = int(gait_data['angle_score'])
            speed_score = int(gait_data['speed_score'])

            # Check if lower limb is detected
            has_lower_limb = gait_data.get('has_lower_limb', False)

            # Update angle progress bar
            if has_lower_limb:
                self.angle_progress.setValue(angle_score)
                self.angle_value_label.setText(f"Score: {angle_score}/100")
            else:
                self.angle_progress.setValue(0)
                self.angle_value_label.setText("No lower limb detected")

            # Update speed progress bar
            self.speed_progress.setValue(speed_score)
            speed = gait_data['speed']
            self.speed_value_label.setText(f"{speed:.2f} m/s (Score: {speed_score}/100)")

            # Update joint angles only if lower limb is detected
            if has_lower_limb:
                angles = gait_data['angles']
                left_knee = angles.get('left_knee', 0)
                right_knee = angles.get('right_knee', 0)

                if hasattr(self, 'left_knee_label'):
                    self.left_knee_label.setText(f"Left Knee: {left_knee:.1f}°")
                if hasattr(self, 'right_knee_label'):
                    self.right_knee_label.setText(f"Right Knee: {right_knee:.1f}°")
            else:
                # Clear angle displays when no lower limb detected
                if hasattr(self, 'left_knee_label'):
                    self.left_knee_label.setText("Left Knee: --")
                if hasattr(self, 'right_knee_label'):
                    self.right_knee_label.setText("Right Knee: --")

            self.status_bar.showMessage(f"Person detected - {status_text}")

        else:
            # No person detected
            self.reset_displays()
            self.status_bar.showMessage("No person detected - Please start walking")

    def reset_displays(self):
        """Reset all displays to default values"""
        self.status_label.setText("No Person Detected")
        self.status_label.setStyleSheet("color: #E74C3C; padding: 10px;")

        self.angle_progress.setValue(0)
        self.angle_value_label.setText("0 / 100")

        self.speed_progress.setValue(0)
        self.speed_value_label.setText("0 / 100")

        self.left_knee_label.setText("Left Knee: 0.0°")
        self.right_knee_label.setText("Right Knee: 0.0°")

        self.speed_display_label.setText("0.00 m/s")

    def apply_styles(self):
        """Apply custom styles to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ECF0F1;
            }
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: #2C3E50;
            }
            QStatusBar {
                background-color: #34495E;
                color: white;
                font-weight: bold;
            }
        """)

    def closeEvent(self, event):
        """Handle application close event"""
        if self.video_thread.running:
            self.video_thread.stop_capture()
        event.accept()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Gait Detection System")
    app.setOrganizationName("Gait Analysis Lab")

    # Set application style
    app.setStyle('Fusion')

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
