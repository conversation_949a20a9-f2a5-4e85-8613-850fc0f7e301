#!/usr/bin/env python3
"""
測試配置檔案載入功能
"""

import sys
import os
sys.path.append('code')

try:
    from gait_analyzer import GaitAnalyzer
    import visualizer as vis
    
    print("測試配置檔案載入...")
    
    # 測試 GaitAnalyzer 配置載入
    analyzer = GaitAnalyzer()
    print("✓ GaitAnalyzer 配置載入成功")
    print(f"  角度閾值: {analyzer.angle_thresholds}")
    print(f"  速度閾值: {analyzer.speed_thresholds}")
    
    # 測試 visualizer 配置載入
    print("✓ Visualizer 配置載入成功")
    print(f"  進度條配置: {vis.VIS_CONFIG['progress_bar']}")
    
    # 測試評估功能
    test_angles = {'left_knee': 90, 'right_knee': 85}
    test_speed = 0.2
    
    level, score, reason = analyzer.evaluate_gait(test_angles, test_speed)
    print(f"\n測試評估結果:")
    print(f"  等級: {level}")
    print(f"  評分: {score:.1f}")
    print(f"  原因: {reason}")
    
    print("\n✓ 所有測試通過！")
    
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    print("請確保已安裝所有依賴套件：pip install -r code/requirements.txt")
except Exception as e:
    print(f"❌ 測試失敗: {e}")
