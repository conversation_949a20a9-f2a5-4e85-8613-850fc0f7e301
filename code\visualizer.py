import cv2
import mediapipe as mp
import numpy as np

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

mp_drawing = mp.solutions.drawing_utils
mp_pose = mp.solutions.pose

# 載入視覺化配置
def _load_visualization_config():
    """載入視覺化配置"""
    if not YAML_AVAILABLE:
        return _get_default_visualization_config()

    try:
        with open('../settings.yaml', 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
            return config['visualization']
    except:
        return _get_default_visualization_config()

def _get_default_visualization_config():
    """返回預設視覺化配置"""
    return {
        'progress_bar': {
            'width': 300,
            'height': 20,
            'position': [50, 120],
            'colors': {
                'excellent': [0, 255, 0],
                'good': [0, 255, 255],
                'poor': [0, 0, 255],
                'background': [64, 64, 64],
                'border': [255, 255, 255]
            },
            'ranges': {
                'excellent': [70, 100],
                'good': [30, 70],
                'poor': [0, 30]
            }
        }
    }

VIS_CONFIG = _load_visualization_config()

def draw_landmarks(image, pose_landmarks):
    if pose_landmarks is None:
        return image
    # pose_landmarks應為mediapipe的NormalizedLandmarkList
    mp_drawing.draw_landmarks(
        image,
        pose_landmarks,
        mp_pose.POSE_CONNECTIONS)
    return image

def draw_angles(image, angles):
    y0 = 30
    for i, (joint, angle) in enumerate(angles.items()):
        text = f"{joint}: {angle:.1f}"
        cv2.putText(image, text, (10, y0 + i*30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,255), 2)
    return image

def draw_gait_result(image, is_normal, speed, reason=None):
    _, w = image.shape[:2]
    if not is_normal and reason:
        gait_text = f"Abnormal gait({reason})"
    else:
        gait_text = "Normal gait" if is_normal else "Abnormal gait"
    color = (0,255,0) if is_normal else (0,0,255)
    cv2.putText(image, gait_text, (w//2-100, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)
    cv2.putText(image, f"Speed: {speed:.2f}", (w//2-100, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,0), 2)
    return image

def draw_progress_bar(image, score, level):
    """
    繪製步態品質進度條
    score: 0-100的評分
    level: 'excellent', 'good', 'poor'
    """
    config = VIS_CONFIG['progress_bar']
    x, y = config['position']
    width = config['width']
    height = config['height']

    # 繪製背景
    cv2.rectangle(image, (x, y), (x + width, y + height),
                 config['colors']['background'], -1)

    # 繪製邊框
    cv2.rectangle(image, (x, y), (x + width, y + height),
                 config['colors']['border'], 2)

    # 計算進度條填充位置
    fill_width = int((score / 100.0) * width)

    # 創建漸變色進度條
    for i in range(fill_width):
        # 計算當前位置的評分比例
        current_score = (i / width) * 100

        # 根據評分計算顏色
        if current_score >= 70:
            # 優良區域：綠色
            color = config['colors']['excellent']
        elif current_score >= 30:
            # 一般區域：黃色到綠色漸變
            ratio = (current_score - 30) / 40
            color = [
                int(config['colors']['good'][0] * (1 - ratio) + config['colors']['excellent'][0] * ratio),
                int(config['colors']['good'][1] * (1 - ratio) + config['colors']['excellent'][1] * ratio),
                int(config['colors']['good'][2] * (1 - ratio) + config['colors']['excellent'][2] * ratio)
            ]
        else:
            # 不佳區域：紅色到黃色漸變
            ratio = current_score / 30
            color = [
                int(config['colors']['poor'][0] * (1 - ratio) + config['colors']['good'][0] * ratio),
                int(config['colors']['poor'][1] * (1 - ratio) + config['colors']['good'][1] * ratio),
                int(config['colors']['poor'][2] * (1 - ratio) + config['colors']['good'][2] * ratio)
            ]

        cv2.line(image, (x + i, y + 1), (x + i, y + height - 1), color, 1)

    # 添加文字標籤
    level_text = {
        'excellent': '優良',
        'good': '一般',
        'poor': '不佳'
    }

    text = f"步態品質: {level_text.get(level, level)} ({score:.1f})"
    cv2.putText(image, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    return image

def draw_gait_result_enhanced(image, level, score, speed, reason=None):
    """
    增強版步態結果顯示，支援三個等級
    """
    _, w = image.shape[:2]

    # 等級對應的中文和顏色
    level_info = {
        'excellent': ('優良步態', (0, 255, 0)),
        'good': ('一般步態', (0, 255, 255)),
        'poor': ('不佳步態', (0, 0, 255))
    }

    gait_text, color = level_info.get(level, ('未知', (128, 128, 128)))

    if level != 'excellent' and reason:
        gait_text += f" ({reason})"

    # 顯示步態狀態
    cv2.putText(image, gait_text, (w//2-100, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)

    # 顯示速度
    cv2.putText(image, f"Speed: {speed:.2f}", (w//2-100, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,0), 2)

    return image

def draw_no_person(image):
    h, w = image.shape[:2]
    # Draw prompt
    cv2.putText(image, "Please start walking", (w//2-180, h//2), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0,0,255), 3)
    # Draw angle and speed as 0
    cv2.putText(image, "left_knee: 0.0", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,255), 2)
    cv2.putText(image, "right_knee: 0.0", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,255,255), 2)
    cv2.putText(image, "Speed: 0.00", (w//2-100, 80), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255,255,0), 2)
    return image
