import numpy as np
from collections import deque

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("Warning: PyYAML not installed, using default configuration")

class GaitAnalyzer:
    def __init__(self, config_path='../settings.yaml'):
        # 載入配置檔案
        self.config = self._load_config(config_path)

        # 從配置中獲取參數
        gait_config = self.config['gait_analysis']
        self.angle_thresholds = gait_config['angle_thresholds']
        self.speed_thresholds = gait_config['speed_thresholds']
        self.scoring = gait_config['scoring']

        # 獲取線性縮放參數
        vis_config = self.config.get('visualization', {})
        linear_config = vis_config.get('progress_bar', {}).get('linear_scaling', {})
        self.speed_scaling = linear_config.get('speed', {
            'min_value': 0.0,
            'max_value': 0.5,
            'excellent_threshold': 0.3,
            'good_threshold': 0.1
        })
        self.angle_scaling = linear_config.get('angle', {
            'ideal_ranges': [[60, 100], [160, 180]],
            'penalty_ranges': [[130, 160]],
            'ideal_weight': 1.0,
            'penalty_weight': -0.5,
            'baseline_score': 50
        })

        # 平滑處理參數
        smoothing = gait_config['smoothing']
        self.speed_window = smoothing['speed_window']
        self.score_window = smoothing['score_window']

        # 系統參數
        self.pixel_to_meter = self.config['system']['pixel_to_meter']

        # 初始化數據緩存
        self.prev_positions = deque(maxlen=self.speed_window)
        self.score_history = deque(maxlen=self.score_window)

    def _load_config(self, config_path):
        """Load YAML configuration file"""
        if not YAML_AVAILABLE:
            print("PyYAML not installed, using default configuration")
            return self._get_default_config()

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found, using default values")
            return self._get_default_config()
        except Exception as e:
            print(f"Configuration file parsing error: {e}, using default values")
            return self._get_default_config()

    def _get_default_config(self):
        """Return default configuration"""
        return {
            'gait_analysis': {
                'angle_thresholds': {
                    'excellent_ranges': [[60, 100], [160, 180]],
                    'good_ranges': [[100, 130], [130, 160]],
                    'poor_range': [130, 160]
                },
                'speed_thresholds': {
                    'excellent': 0.3,
                    'good': 0.1,
                    'poor': 0.1
                },
                'scoring': {
                    'angle_weight': 0.6,
                    'speed_weight': 0.4
                },
                'smoothing': {
                    'speed_window': 5,
                    'score_window': 3
                }
            },
            'system': {
                'pixel_to_meter': None
            }
        }

    def evaluate_gait(self, joint_angles, speed, has_lower_limb=True):
        """
        Evaluate gait quality, returns three levels: excellent, good, poor
        Returns: (level, score, main_reason)
        """
        # 計算角度評分（使用線性縮放）
        if has_lower_limb:
            angle_score = self._evaluate_angles_linear(joint_angles)
        else:
            angle_score = 0

        # 計算速度評分（使用線性縮放）
        speed_score = self._evaluate_speed_linear(speed)

        # 綜合評分
        total_score = (angle_score * self.scoring['angle_weight'] +
                      speed_score * self.scoring['speed_weight'])

        # 平滑處理評分
        self.score_history.append(total_score)
        smoothed_score = np.mean(list(self.score_history))

        # 判斷等級
        if smoothed_score >= 70:
            level = 'excellent'
            reason = ''
        elif smoothed_score >= 30:
            level = 'good'
            reason = self._get_main_issue(angle_score, speed_score)
        else:
            level = 'poor'
            reason = self._get_main_issue(angle_score, speed_score)

        return level, smoothed_score, reason

    def has_lower_limb_landmarks(self, landmarks):
        """檢查是否有下肢關鍵點"""
        if landmarks is None:
            return False

        # MediaPipe pose landmarks indices for lower limb
        lower_limb_indices = [23, 24, 25, 26, 27, 28]  # Hip, knee, ankle points

        # Check if at least 4 out of 6 lower limb landmarks are detected
        detected_count = 0
        for idx in lower_limb_indices:
            if idx < len(landmarks.landmark):
                landmark = landmarks.landmark[idx]
                # Check if landmark is visible (visibility > 0.5)
                if hasattr(landmark, 'visibility') and landmark.visibility > 0.5:
                    detected_count += 1

        return detected_count >= 4

    def _evaluate_speed_linear(self, speed):
        """使用線性縮放評估速度評分"""
        min_val = self.speed_scaling['min_value']
        max_val = self.speed_scaling['max_value']

        # 線性縮放到 0-100
        if speed <= min_val:
            return 0
        elif speed >= max_val:
            return 100
        else:
            return ((speed - min_val) / (max_val - min_val)) * 100

    def _evaluate_angles_linear(self, joint_angles):
        """使用線性縮放評估角度評分"""
        left_knee = joint_angles.get('left_knee', 0)
        right_knee = joint_angles.get('right_knee', 0)

        # 計算每個膝蓋的評分
        left_score = self._calculate_angle_score(left_knee)
        right_score = self._calculate_angle_score(right_knee)

        # 返回平均評分
        return (left_score + right_score) / 2

    def _calculate_angle_score(self, angle):
        """計算單個角度的評分"""
        score = self.angle_scaling['baseline_score']

        # 檢查理想範圍
        for ideal_range in self.angle_scaling['ideal_ranges']:
            if ideal_range[0] <= angle <= ideal_range[1]:
                # 在理想範圍內，根據距離中心點的遠近給分
                center = (ideal_range[0] + ideal_range[1]) / 2
                range_width = ideal_range[1] - ideal_range[0]
                distance_from_center = abs(angle - center)
                # 距離中心越近分數越高
                range_score = 100 - (distance_from_center / (range_width / 2)) * 50
                score += range_score * self.angle_scaling['ideal_weight']
                break

        # 檢查懲罰範圍
        for penalty_range in self.angle_scaling['penalty_ranges']:
            if penalty_range[0] <= angle <= penalty_range[1]:
                penalty_score = 50  # 固定懲罰分數
                score += penalty_score * self.angle_scaling['penalty_weight']
                break

        # 確保評分在 0-100 範圍內
        return max(0, min(100, score))

    def _evaluate_angles(self, joint_angles):
        """Evaluate joint angles, returns 0-100 score"""
        left_knee = joint_angles.get('left_knee', 0)
        right_knee = joint_angles.get('right_knee', 0)

        # 檢查是否在優良範圍
        excellent_ranges = self.angle_thresholds['excellent_ranges']
        good_ranges = self.angle_thresholds['good_ranges']
        poor_range = self.angle_thresholds['poor_range']

        left_excellent = any(r[0] <= left_knee <= r[1] for r in excellent_ranges)
        right_excellent = any(r[0] <= right_knee <= r[1] for r in excellent_ranges)

        left_good = any(r[0] <= left_knee <= r[1] for r in good_ranges)
        right_good = any(r[0] <= right_knee <= r[1] for r in good_ranges)

        # 檢查不佳條件（兩腿同時在異常範圍）
        both_poor = (poor_range[0] <= left_knee <= poor_range[1] and
                    poor_range[0] <= right_knee <= poor_range[1])

        if both_poor:
            return 10  # 不佳評分
        elif left_excellent and right_excellent:
            return 90  # 優良評分
        elif (left_excellent or right_excellent) and (left_good or right_good):
            return 60  # 一般評分
        elif left_good and right_good:
            return 50  # 一般評分
        else:
            return 20  # 較差評分

    def _evaluate_speed(self, speed):
        """Evaluate speed, returns 0-100 score"""
        if speed >= self.speed_thresholds['excellent']:
            return 90  # 優良
        elif speed >= self.speed_thresholds['good']:
            # 線性插值計算一般範圍內的評分
            ratio = (speed - self.speed_thresholds['good']) / (
                self.speed_thresholds['excellent'] - self.speed_thresholds['good'])
            return 30 + ratio * 40  # 30-70範圍
        else:
            # 速度過低
            return 10

    def _get_main_issue(self, angle_score, speed_score):
        """Determine main issue source"""
        if angle_score < speed_score:
            return 'Angle'
        elif speed_score < angle_score:
            return 'Speed'
        else:
            return 'Both'

    def calculate_speed(self, pose_landmarks, dt):
        """Calculate walking speed"""
        # Use left hip as example
        LEFT_HIP = 23
        if pose_landmarks is None:
            return 0.0

        lm = pose_landmarks.landmark
        x = lm[LEFT_HIP].x
        y = lm[LEFT_HIP].y
        self.prev_positions.append((x, y))

        if len(self.prev_positions) < 2:
            return 0.0

        # 計算移動距離
        dx = self.prev_positions[-1][0] - self.prev_positions[0][0]
        dy = self.prev_positions[-1][1] - self.prev_positions[0][1]
        dist = np.sqrt(dx**2 + dy**2)

        # 轉換為實際距離（如果有標定）
        if self.pixel_to_meter:
            dist = dist * self.pixel_to_meter

        # 計算速度
        time_span = dt * (len(self.prev_positions) - 1)
        speed = dist / time_span if time_span > 0 else 0.0

        return speed

    # Backward compatibility method
    def is_normal_gait(self, joint_angles, speed):
        """Backward compatibility method, converts new three-level evaluation to binary result"""
        level, _, reason = self.evaluate_gait(joint_angles, speed)
        is_normal = level in ['excellent', 'good']
        return is_normal, reason