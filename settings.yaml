# Gait Detection System Configuration File
# Walking Gait Detection and Speed Calculation System

gait_analysis:
  # Angle condition thresholds (knee joint angles, unit: degrees)
  angle_thresholds:
    # Excellent range: normal walking angles
    excellent_ranges:
      - [60, 100]   # Knee flexion range
      - [160, 180]  # Knee extension range

    # Good range: slightly abnormal angles
    good_ranges:
      - [100, 130]  # Slight over-flexion
      - [130, 160]  # Moderate abnormal range

    # Poor range: severely abnormal angles (both legs simultaneously)
    poor_range: [130, 160]

  # Speed condition thresholds (unit: m/s or relative speed)
  speed_thresholds:
    excellent: 0.3    # Above this value is excellent
    good: 0.1         # Between good and excellent is good
    poor: 0.1         # Below this value is poor

  # Scoring weight settings
  scoring:
    angle_weight: 0.6    # Angle condition weight
    speed_weight: 0.4    # Speed condition weight

  # Smoothing parameters
  smoothing:
    speed_window: 5      # Speed smoothing window size
    score_window: 3      # Score smoothing window size

# Visualization settings
visualization:
  # Progress bar settings
  progress_bar:
    width: 300           # Progress bar width
    height: 20           # Progress bar height
    position: [50, 120]  # Progress bar position [x, y]

    # Color settings (BGR format)
    colors:
      excellent: [0, 255, 0]    # Green
      good: [0, 255, 255]       # Yellow
      poor: [0, 0, 255]         # Red
      background: [64, 64, 64]  # Background gray
      border: [255, 255, 255]   # Border white

    # Range segments for linear progression
    ranges:
      excellent: [70, 100]  # Excellent range
      good: [30, 70]        # Good range
      poor: [0, 30]         # Poor range

    # Linear scaling parameters
    linear_scaling:
      # Walking speed progress bar scaling
      speed:
        min_value: 0.0      # Minimum speed for 0% progress
        max_value: 0.5      # Maximum speed for 100% progress
        excellent_threshold: 0.3  # Speed for excellent rating
        good_threshold: 0.1       # Speed for good rating

      # Lower limb angle progress bar scaling
      angle:
        # Ideal angle ranges (higher score for being in these ranges)
        ideal_ranges:
          - [60, 100]   # Knee flexion range
          - [160, 180]  # Knee extension range
        # Penalty ranges (lower score for being in these ranges)
        penalty_ranges:
          - [130, 160]  # Problematic range
        # Linear scaling factors
        ideal_weight: 1.0     # Weight for ideal range
        penalty_weight: -0.5  # Weight for penalty range
        baseline_score: 50    # Baseline score when not in any specific range

# GUI settings
gui:
  window:
    title: "Gait Detection System"
    width: 1200
    height: 800
    min_width: 800
    min_height: 600

  colors:
    primary: "#2C3E50"      # Dark blue-gray
    secondary: "#34495E"    # Lighter blue-gray
    accent: "#3498DB"       # Blue
    success: "#27AE60"      # Green
    warning: "#F39C12"      # Orange
    danger: "#E74C3C"       # Red
    background: "#ECF0F1"   # Light gray
    text: "#2C3E50"         # Dark text
    text_light: "#7F8C8D"   # Light text

# System parameters
system:
  pixel_to_meter: null    # Pixel to meter conversion ratio, null means use relative speed
  fps_target: 30          # Target frame rate
