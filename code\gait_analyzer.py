import numpy as np
from collections import deque

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("警告: PyYAML 未安裝，使用預設配置")

class GaitAnalyzer:
    def __init__(self, config_path='../settings.yaml'):
        # 載入配置檔案
        self.config = self._load_config(config_path)

        # 從配置中獲取參數
        gait_config = self.config['gait_analysis']
        self.angle_thresholds = gait_config['angle_thresholds']
        self.speed_thresholds = gait_config['speed_thresholds']
        self.scoring = gait_config['scoring']

        # 平滑處理參數
        smoothing = gait_config['smoothing']
        self.speed_window = smoothing['speed_window']
        self.score_window = smoothing['score_window']

        # 系統參數
        self.pixel_to_meter = self.config['system']['pixel_to_meter']

        # 初始化數據緩存
        self.prev_positions = deque(maxlen=self.speed_window)
        self.score_history = deque(maxlen=self.score_window)

    def _load_config(self, config_path):
        """載入YAML配置檔案"""
        if not YAML_AVAILABLE:
            print("PyYAML 未安裝，使用預設配置")
            return self._get_default_config()

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"配置檔案 {config_path} 未找到，使用預設值")
            return self._get_default_config()
        except Exception as e:
            print(f"配置檔案解析錯誤: {e}，使用預設值")
            return self._get_default_config()

    def _get_default_config(self):
        """返回預設配置"""
        return {
            'gait_analysis': {
                'angle_thresholds': {
                    'excellent_ranges': [[60, 100], [160, 180]],
                    'good_ranges': [[100, 130], [130, 160]],
                    'poor_range': [130, 160]
                },
                'speed_thresholds': {
                    'excellent': 0.3,
                    'good': 0.1,
                    'poor': 0.1
                },
                'scoring': {
                    'angle_weight': 0.6,
                    'speed_weight': 0.4
                },
                'smoothing': {
                    'speed_window': 5,
                    'score_window': 3
                }
            },
            'system': {
                'pixel_to_meter': None
            }
        }

    def evaluate_gait(self, joint_angles, speed):
        """
        評估步態品質，返回三個等級：excellent, good, poor
        返回: (等級, 評分, 主要原因)
        """
        # 計算角度評分
        angle_score = self._evaluate_angles(joint_angles)

        # 計算速度評分
        speed_score = self._evaluate_speed(speed)

        # 綜合評分
        total_score = (angle_score * self.scoring['angle_weight'] +
                      speed_score * self.scoring['speed_weight'])

        # 平滑處理評分
        self.score_history.append(total_score)
        smoothed_score = np.mean(list(self.score_history))

        # 判斷等級
        if smoothed_score >= 70:
            level = 'excellent'
            reason = ''
        elif smoothed_score >= 30:
            level = 'good'
            reason = self._get_main_issue(angle_score, speed_score)
        else:
            level = 'poor'
            reason = self._get_main_issue(angle_score, speed_score)

        return level, smoothed_score, reason

    def _evaluate_angles(self, joint_angles):
        """評估關節角度，返回0-100的評分"""
        left_knee = joint_angles.get('left_knee', 0)
        right_knee = joint_angles.get('right_knee', 0)

        # 檢查是否在優良範圍
        excellent_ranges = self.angle_thresholds['excellent_ranges']
        good_ranges = self.angle_thresholds['good_ranges']
        poor_range = self.angle_thresholds['poor_range']

        left_excellent = any(r[0] <= left_knee <= r[1] for r in excellent_ranges)
        right_excellent = any(r[0] <= right_knee <= r[1] for r in excellent_ranges)

        left_good = any(r[0] <= left_knee <= r[1] for r in good_ranges)
        right_good = any(r[0] <= right_knee <= r[1] for r in good_ranges)

        # 檢查不佳條件（兩腿同時在異常範圍）
        both_poor = (poor_range[0] <= left_knee <= poor_range[1] and
                    poor_range[0] <= right_knee <= poor_range[1])

        if both_poor:
            return 10  # 不佳評分
        elif left_excellent and right_excellent:
            return 90  # 優良評分
        elif (left_excellent or right_excellent) and (left_good or right_good):
            return 60  # 一般評分
        elif left_good and right_good:
            return 50  # 一般評分
        else:
            return 20  # 較差評分

    def _evaluate_speed(self, speed):
        """評估速度，返回0-100的評分"""
        if speed >= self.speed_thresholds['excellent']:
            return 90  # 優良
        elif speed >= self.speed_thresholds['good']:
            # 線性插值計算一般範圍內的評分
            ratio = (speed - self.speed_thresholds['good']) / (
                self.speed_thresholds['excellent'] - self.speed_thresholds['good'])
            return 30 + ratio * 40  # 30-70範圍
        else:
            # 速度過低
            return 10

    def _get_main_issue(self, angle_score, speed_score):
        """判斷主要問題來源"""
        if angle_score < speed_score:
            return 'Angle'
        elif speed_score < angle_score:
            return 'Speed'
        else:
            return 'Both'

    def calculate_speed(self, pose_landmarks, dt):
        """計算行走速度"""
        # Use left hip as example
        LEFT_HIP = 23
        if pose_landmarks is None:
            return 0.0

        lm = pose_landmarks.landmark
        x = lm[LEFT_HIP].x
        y = lm[LEFT_HIP].y
        self.prev_positions.append((x, y))

        if len(self.prev_positions) < 2:
            return 0.0

        # 計算移動距離
        dx = self.prev_positions[-1][0] - self.prev_positions[0][0]
        dy = self.prev_positions[-1][1] - self.prev_positions[0][1]
        dist = np.sqrt(dx**2 + dy**2)

        # 轉換為實際距離（如果有標定）
        if self.pixel_to_meter:
            dist = dist * self.pixel_to_meter

        # 計算速度
        time_span = dt * (len(self.prev_positions) - 1)
        speed = dist / time_span if time_span > 0 else 0.0

        return speed

    # 保持向後相容性的舊方法
    def is_normal_gait(self, joint_angles, speed):
        """向後相容性方法，將新的三級評估轉換為二元結果"""
        level, _, reason = self.evaluate_gait(joint_angles, speed)
        is_normal = level in ['excellent', 'good']
        return is_normal, reason