#!/usr/bin/env python3

print("Starting simple test...")

try:
    import yaml
    print("✓ PyYAML imported successfully")
except ImportError as e:
    print(f"❌ PyYAML import failed: {e}")
    exit(1)

try:
    from gait_analyzer import Gait<PERSON>nalyzer
    print("✓ GaitAnalyzer imported successfully")
    
    analyzer = GaitAnalyzer()
    print("✓ GaitAnalyzer initialized successfully")
    
    # Test evaluation
    test_angles = {'left_knee': 90, 'right_knee': 85}
    test_speed = 0.2
    
    level, score, reason = analyzer.evaluate_gait(test_angles, test_speed)
    print(f"✓ Evaluation test:")
    print(f"  Level: {level}")
    print(f"  Score: {score:.1f}")
    print(f"  Reason: {reason}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
